<?php
/**
 * Admin Class
 *
 * @package SchedSpot
 * @version 0.1.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SchedSpot_Admin Class.
 *
 * @class SchedSpot_Admin
 * @version 0.1.0
 */
class SchedSpot_Admin {

    /**
     * Constructor.
     *
     * @since 0.1.0
     */
    public function __construct() {
        $this->init();
    }

    /**
     * Initialize admin functionality.
     *
     * @since 0.1.0
     */
    public function init() {
        add_action( 'admin_menu', array( $this, 'admin_menu' ) );
        add_action( 'admin_init', array( $this, 'admin_init' ) );
        add_action( 'admin_enqueue_scripts', array( $this, 'admin_scripts' ) );
        add_filter( 'plugin_action_links_' . SCHEDSPOT_PLUGIN_BASENAME, array( $this, 'plugin_action_links' ) );
    }

    /**
     * Add admin menu items.
     *
     * @since 0.1.0
     */
    public function admin_menu() {
        // Main menu
        add_menu_page(
            __( 'SchedSpot', 'schedspot' ),
            __( 'SchedSpot', 'schedspot' ),
            'manage_options',
            'schedspot',
            array( $this, 'dashboard_page' ),
            'dashicons-calendar-alt',
            30
        );

        // Bookings submenu
        add_submenu_page(
            'schedspot',
            __( 'Bookings', 'schedspot' ),
            __( 'Bookings', 'schedspot' ),
            'manage_options',
            'schedspot-bookings',
            array( $this, 'bookings_page' )
        );

        // Services submenu
        add_submenu_page(
            'schedspot',
            __( 'Services', 'schedspot' ),
            __( 'Services', 'schedspot' ),
            'manage_options',
            'schedspot-services',
            array( $this, 'services_page' )
        );

        // Workers submenu
        add_submenu_page(
            'schedspot',
            __( 'Workers', 'schedspot' ),
            __( 'Workers', 'schedspot' ),
            'manage_options',
            'schedspot-workers',
            array( $this, 'workers_page' )
        );

        // Settings submenu
        add_submenu_page(
            'schedspot',
            __( 'Settings', 'schedspot' ),
            __( 'Settings', 'schedspot' ),
            'manage_options',
            'schedspot-settings',
            array( $this, 'settings_page' )
        );
    }

    /**
     * Initialize admin settings.
     *
     * @since 0.1.0
     */
    public function admin_init() {
        // Register settings
        register_setting( 'schedspot_general_settings', 'schedspot_default_timezone' );
        register_setting( 'schedspot_general_settings', 'schedspot_date_format' );
        register_setting( 'schedspot_general_settings', 'schedspot_time_format' );
        register_setting( 'schedspot_general_settings', 'schedspot_currency' );

        register_setting( 'schedspot_booking_settings', 'schedspot_default_slot_length' );
        register_setting( 'schedspot_booking_settings', 'schedspot_minimum_notice' );
        register_setting( 'schedspot_booking_settings', 'schedspot_auto_approve_bookings' );

        register_setting( 'schedspot_payment_settings', 'schedspot_system_fee_per_hour' );
        register_setting( 'schedspot_payment_settings', 'schedspot_commission_rate' );

        // Add settings sections
        add_settings_section(
            'schedspot_general_section',
            __( 'General Settings', 'schedspot' ),
            array( $this, 'general_section_callback' ),
            'schedspot_general_settings'
        );

        add_settings_section(
            'schedspot_booking_section',
            __( 'Booking Settings', 'schedspot' ),
            array( $this, 'booking_section_callback' ),
            'schedspot_booking_settings'
        );

        add_settings_section(
            'schedspot_payment_section',
            __( 'Payment Settings', 'schedspot' ),
            array( $this, 'payment_section_callback' ),
            'schedspot_payment_settings'
        );

        // Add settings fields
        $this->add_settings_fields();
    }

    /**
     * Add settings fields.
     *
     * @since 0.1.0
     */
    private function add_settings_fields() {
        // General settings fields
        add_settings_field(
            'schedspot_default_timezone',
            __( 'Default Timezone', 'schedspot' ),
            array( $this, 'timezone_field_callback' ),
            'schedspot_general_settings',
            'schedspot_general_section'
        );

        add_settings_field(
            'schedspot_date_format',
            __( 'Date Format', 'schedspot' ),
            array( $this, 'date_format_field_callback' ),
            'schedspot_general_settings',
            'schedspot_general_section'
        );

        add_settings_field(
            'schedspot_time_format',
            __( 'Time Format', 'schedspot' ),
            array( $this, 'time_format_field_callback' ),
            'schedspot_general_settings',
            'schedspot_general_section'
        );

        add_settings_field(
            'schedspot_currency',
            __( 'Currency', 'schedspot' ),
            array( $this, 'currency_field_callback' ),
            'schedspot_general_settings',
            'schedspot_general_section'
        );

        // Booking settings fields
        add_settings_field(
            'schedspot_default_slot_length',
            __( 'Default Slot Length (minutes)', 'schedspot' ),
            array( $this, 'slot_length_field_callback' ),
            'schedspot_booking_settings',
            'schedspot_booking_section'
        );

        add_settings_field(
            'schedspot_minimum_notice',
            __( 'Minimum Notice (hours)', 'schedspot' ),
            array( $this, 'minimum_notice_field_callback' ),
            'schedspot_booking_settings',
            'schedspot_booking_section'
        );

        add_settings_field(
            'schedspot_auto_approve_bookings',
            __( 'Auto-approve Bookings', 'schedspot' ),
            array( $this, 'auto_approve_field_callback' ),
            'schedspot_booking_settings',
            'schedspot_booking_section'
        );

        // Payment settings fields
        add_settings_field(
            'schedspot_system_fee_per_hour',
            __( 'System Fee per Hour ($)', 'schedspot' ),
            array( $this, 'system_fee_field_callback' ),
            'schedspot_payment_settings',
            'schedspot_payment_section'
        );

        add_settings_field(
            'schedspot_commission_rate',
            __( 'Commission Rate (%)', 'schedspot' ),
            array( $this, 'commission_rate_field_callback' ),
            'schedspot_payment_settings',
            'schedspot_payment_section'
        );
    }

    /**
     * Enqueue admin scripts and styles.
     *
     * @since 0.1.0
     * @param string $hook Current admin page hook.
     */
    public function admin_scripts( $hook ) {
        // Only load on SchedSpot admin pages
        if ( strpos( $hook, 'schedspot' ) === false ) {
            return;
        }

        // Add inline admin styles for now
        wp_add_inline_style( 'wp-admin', $this->get_admin_styles() );
    }

    /**
     * Add plugin action links.
     *
     * @since 0.1.0
     * @param array $links Existing action links.
     * @return array Modified action links.
     */
    public function plugin_action_links( $links ) {
        $settings_link = '<a href="' . admin_url( 'admin.php?page=schedspot-settings' ) . '">' . __( 'Settings', 'schedspot' ) . '</a>';
        array_unshift( $links, $settings_link );
        return $links;
    }

    /**
     * Dashboard page callback.
     *
     * @since 0.1.0
     */
    public function dashboard_page() {
        ?>
        <div class="wrap">
            <h1><?php _e( 'SchedSpot Dashboard', 'schedspot' ); ?></h1>
            
            <div class="schedspot-dashboard-widgets">
                <div class="schedspot-widget">
                    <h3><?php _e( 'Recent Bookings', 'schedspot' ); ?></h3>
                    <?php $this->render_recent_bookings_widget(); ?>
                </div>
                
                <div class="schedspot-widget">
                    <h3><?php _e( 'Quick Stats', 'schedspot' ); ?></h3>
                    <?php $this->render_quick_stats_widget(); ?>
                </div>
                
                <div class="schedspot-widget">
                    <h3><?php _e( 'Quick Actions', 'schedspot' ); ?></h3>
                    <?php $this->render_quick_actions_widget(); ?>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Bookings page callback.
     *
     * @since 0.1.0
     */
    public function bookings_page() {
        ?>
        <div class="wrap">
            <h1><?php _e( 'Bookings', 'schedspot' ); ?></h1>
            
            <?php $this->render_bookings_list(); ?>
        </div>
        <?php
    }

    /**
     * Services page callback.
     *
     * @since 1.0.0
     */
    public function services_page() {
        $action = isset( $_GET['action'] ) ? sanitize_text_field( $_GET['action'] ) : '';
        $service_id = isset( $_GET['service_id'] ) ? absint( $_GET['service_id'] ) : 0;

        // Handle form submissions
        if ( isset( $_POST['schedspot_service_action'] ) ) {
            $this->handle_service_form_submission();
        }

        switch ( $action ) {
            case 'add':
                $this->render_add_service_form();
                break;
            case 'edit':
                $this->render_edit_service_form( $service_id );
                break;
            case 'delete':
                $this->handle_delete_service( $service_id );
                break;
            default:
                $this->render_services_list();
                break;
        }
    }

    /**
     * Workers page callback.
     *
     * @since 1.0.0
     */
    public function workers_page() {
        $action = isset( $_GET['action'] ) ? sanitize_text_field( $_GET['action'] ) : '';
        $worker_id = isset( $_GET['worker_id'] ) ? absint( $_GET['worker_id'] ) : 0;

        // Handle form submissions
        if ( isset( $_POST['schedspot_worker_action'] ) ) {
            $this->handle_worker_form_submission();
        }

        switch ( $action ) {
            case 'add':
                $this->render_add_worker_form();
                break;
            case 'edit':
                $this->render_edit_worker_form( $worker_id );
                break;
            case 'view':
                $this->render_worker_profile( $worker_id );
                break;
            case 'availability':
                $this->render_worker_availability( $worker_id );
                break;
            default:
                $this->render_workers_list();
                break;
        }
    }

    /**
     * Settings page callback.
     *
     * @since 0.1.0
     */
    public function settings_page() {
        $active_tab = isset( $_GET['tab'] ) ? $_GET['tab'] : 'general';
        ?>
        <div class="wrap">
            <h1><?php _e( 'SchedSpot Settings', 'schedspot' ); ?></h1>
            
            <h2 class="nav-tab-wrapper">
                <a href="?page=schedspot-settings&tab=general" class="nav-tab <?php echo $active_tab == 'general' ? 'nav-tab-active' : ''; ?>"><?php _e( 'General', 'schedspot' ); ?></a>
                <a href="?page=schedspot-settings&tab=booking" class="nav-tab <?php echo $active_tab == 'booking' ? 'nav-tab-active' : ''; ?>"><?php _e( 'Booking', 'schedspot' ); ?></a>
                <a href="?page=schedspot-settings&tab=payment" class="nav-tab <?php echo $active_tab == 'payment' ? 'nav-tab-active' : ''; ?>"><?php _e( 'Payment', 'schedspot' ); ?></a>
            </h2>
            
            <form method="post" action="options.php">
                <?php
                if ( $active_tab == 'general' ) {
                    settings_fields( 'schedspot_general_settings' );
                    do_settings_sections( 'schedspot_general_settings' );
                } elseif ( $active_tab == 'booking' ) {
                    settings_fields( 'schedspot_booking_settings' );
                    do_settings_sections( 'schedspot_booking_settings' );
                } elseif ( $active_tab == 'payment' ) {
                    settings_fields( 'schedspot_payment_settings' );
                    do_settings_sections( 'schedspot_payment_settings' );
                }
                submit_button();
                ?>
            </form>
        </div>
        <?php
    }

    /**
     * Render recent bookings widget.
     *
     * @since 0.1.0
     */
    private function render_recent_bookings_widget() {
        $bookings = SchedSpot_Booking::get_bookings( array( 'limit' => 5 ) );
        
        if ( ! empty( $bookings ) ) {
            echo '<table class="wp-list-table widefat fixed striped">';
            echo '<thead><tr><th>' . __( 'Client', 'schedspot' ) . '</th><th>' . __( 'Date', 'schedspot' ) . '</th><th>' . __( 'Status', 'schedspot' ) . '</th></tr></thead>';
            echo '<tbody>';
            
            foreach ( $bookings as $booking ) {
                echo '<tr>';
                echo '<td>' . esc_html( $booking->client_details['name'] ) . '</td>';
                echo '<td>' . esc_html( date( 'M j, Y', strtotime( $booking->booking_date ) ) ) . '</td>';
                echo '<td>' . esc_html( ucfirst( $booking->status ) ) . '</td>';
                echo '</tr>';
            }
            
            echo '</tbody></table>';
        } else {
            echo '<p>' . __( 'No bookings found.', 'schedspot' ) . '</p>';
        }
    }

    /**
     * Render quick stats widget.
     *
     * @since 0.1.0
     */
    private function render_quick_stats_widget() {
        global $wpdb;
        
        $total_bookings = $wpdb->get_var( "SELECT COUNT(*) FROM {$wpdb->prefix}schedspot_bookings" );
        $pending_bookings = $wpdb->get_var( "SELECT COUNT(*) FROM {$wpdb->prefix}schedspot_bookings WHERE status = 'pending'" );
        $total_workers = count( get_users( array( 'role' => 'schedspot_worker' ) ) );
        $total_customers = count( get_users( array( 'role' => 'schedspot_customer' ) ) );
        
        ?>
        <div class="schedspot-stats-grid">
            <div class="stat-item">
                <div class="stat-number"><?php echo esc_html( $total_bookings ); ?></div>
                <div class="stat-label"><?php _e( 'Total Bookings', 'schedspot' ); ?></div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?php echo esc_html( $pending_bookings ); ?></div>
                <div class="stat-label"><?php _e( 'Pending Bookings', 'schedspot' ); ?></div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?php echo esc_html( $total_workers ); ?></div>
                <div class="stat-label"><?php _e( 'Workers', 'schedspot' ); ?></div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?php echo esc_html( $total_customers ); ?></div>
                <div class="stat-label"><?php _e( 'Customers', 'schedspot' ); ?></div>
            </div>
        </div>
        <?php
    }

    /**
     * Render quick actions widget.
     *
     * @since 0.1.0
     */
    private function render_quick_actions_widget() {
        ?>
        <div class="schedspot-quick-actions">
            <a href="<?php echo admin_url( 'admin.php?page=schedspot-bookings' ); ?>" class="button button-primary"><?php _e( 'View All Bookings', 'schedspot' ); ?></a>
            <a href="<?php echo admin_url( 'admin.php?page=schedspot-workers' ); ?>" class="button"><?php _e( 'Manage Workers', 'schedspot' ); ?></a>
            <a href="<?php echo admin_url( 'admin.php?page=schedspot-settings' ); ?>" class="button"><?php _e( 'Settings', 'schedspot' ); ?></a>
        </div>
        <?php
    }

    /**
     * Render bookings list.
     *
     * @since 0.1.0
     */
    private function render_bookings_list() {
        $bookings = SchedSpot_Booking::get_bookings( array( 'limit' => 50 ) );
        
        ?>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th><?php _e( 'ID', 'schedspot' ); ?></th>
                    <th><?php _e( 'Client', 'schedspot' ); ?></th>
                    <th><?php _e( 'Worker', 'schedspot' ); ?></th>
                    <th><?php _e( 'Date', 'schedspot' ); ?></th>
                    <th><?php _e( 'Time', 'schedspot' ); ?></th>
                    <th><?php _e( 'Status', 'schedspot' ); ?></th>
                    <th><?php _e( 'Total', 'schedspot' ); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php if ( ! empty( $bookings ) ) : ?>
                    <?php foreach ( $bookings as $booking ) : ?>
                        <tr>
                            <td><?php echo esc_html( $booking->id ); ?></td>
                            <td><?php echo esc_html( $booking->client_details['name'] ); ?></td>
                            <td><?php echo esc_html( get_userdata( $booking->worker_id )->display_name ); ?></td>
                            <td><?php echo esc_html( date( 'M j, Y', strtotime( $booking->booking_date ) ) ); ?></td>
                            <td><?php echo esc_html( date( 'g:i A', strtotime( $booking->start_time ) ) ); ?></td>
                            <td><?php echo esc_html( ucfirst( $booking->status ) ); ?></td>
                            <td>$<?php echo esc_html( number_format( $booking->total_cost, 2 ) ); ?></td>
                        </tr>
                    <?php endforeach; ?>
                <?php else : ?>
                    <tr>
                        <td colspan="7"><?php _e( 'No bookings found.', 'schedspot' ); ?></td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
        <?php
    }

    /**
     * Render workers list.
     *
     * @since 1.0.0
     */
    private function render_workers_list() {
        $workers = SchedSpot_Worker::get_workers( array( 'number' => 50 ) );

        ?>
        <div class="wrap">
            <h1 class="wp-heading-inline"><?php _e( 'Workers', 'schedspot' ); ?></h1>
            <a href="<?php echo admin_url( 'admin.php?page=schedspot-workers&action=add' ); ?>" class="page-title-action"><?php _e( 'Add New', 'schedspot' ); ?></a>
            <hr class="wp-header-end">

            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e( 'Worker', 'schedspot' ); ?></th>
                        <th><?php _e( 'Contact', 'schedspot' ); ?></th>
                        <th><?php _e( 'Profile', 'schedspot' ); ?></th>
                        <th><?php _e( 'Services', 'schedspot' ); ?></th>
                        <th><?php _e( 'Bookings', 'schedspot' ); ?></th>
                        <th><?php _e( 'Status', 'schedspot' ); ?></th>
                        <th><?php _e( 'Actions', 'schedspot' ); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ( ! empty( $workers ) ) : ?>
                        <?php foreach ( $workers as $worker ) : ?>
                            <?php $stats = $worker->get_statistics(); ?>
                            <tr>
                                <td>
                                    <div style="display: flex; align-items: center;">
                                        <img src="<?php echo esc_url( get_avatar_url( $worker->id, array( 'size' => 32 ) ) ); ?>" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 10px;">
                                        <div>
                                            <strong><?php echo esc_html( $worker->user->display_name ); ?></strong>
                                            <br><small>ID: <?php echo esc_html( $worker->id ); ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <?php echo esc_html( $worker->user->user_email ); ?><br>
                                    <small><?php echo esc_html( $worker->profile['phone'] ); ?></small>
                                </td>
                                <td>
                                    <div class="schedspot-progress-bar" style="background: #f0f0f0; border-radius: 10px; height: 20px; position: relative;">
                                        <div style="background: #0073aa; height: 100%; border-radius: 10px; width: <?php echo esc_attr( $stats['profile_completion'] ); ?>%;"></div>
                                        <span style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 12px; color: #333;">
                                            <?php echo esc_html( $stats['profile_completion'] ); ?>%
                                        </span>
                                    </div>
                                    <small><?php printf( __( 'Rating: %.1f', 'schedspot' ), $stats['rating'] ); ?></small>
                                </td>
                                <td>
                                    <?php
                                    $services = $worker->get_services();
                                    echo esc_html( count( $services ) );
                                    ?>
                                </td>
                                <td>
                                    <?php echo esc_html( $stats['total_bookings'] ); ?><br>
                                    <small><?php printf( __( '%.1f%% completion', 'schedspot' ), $stats['completion_rate'] ); ?></small>
                                </td>
                                <td>
                                    <?php if ( $worker->profile['is_available'] ) : ?>
                                        <span style="color: green;">●</span> <?php _e( 'Available', 'schedspot' ); ?>
                                    <?php else : ?>
                                        <span style="color: red;">●</span> <?php _e( 'Unavailable', 'schedspot' ); ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="<?php echo admin_url( 'admin.php?page=schedspot-workers&action=view&worker_id=' . $worker->id ); ?>" class="button button-small"><?php _e( 'View', 'schedspot' ); ?></a>
                                    <a href="<?php echo admin_url( 'admin.php?page=schedspot-workers&action=edit&worker_id=' . $worker->id ); ?>" class="button button-small"><?php _e( 'Edit', 'schedspot' ); ?></a>
                                    <a href="<?php echo admin_url( 'admin.php?page=schedspot-workers&action=availability&worker_id=' . $worker->id ); ?>" class="button button-small"><?php _e( 'Schedule', 'schedspot' ); ?></a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else : ?>
                        <tr>
                            <td colspan="7"><?php _e( 'No workers found.', 'schedspot' ); ?></td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        <?php
    }

    // Settings field callbacks
    public function general_section_callback() {
        echo '<p>' . __( 'Configure general plugin settings.', 'schedspot' ) . '</p>';
    }

    public function booking_section_callback() {
        echo '<p>' . __( 'Configure booking-related settings.', 'schedspot' ) . '</p>';
    }

    public function payment_section_callback() {
        echo '<p>' . __( 'Configure payment and commission settings.', 'schedspot' ) . '</p>';
    }

    public function timezone_field_callback() {
        $value = get_option( 'schedspot_default_timezone', 'UTC' );
        echo '<select name="schedspot_default_timezone">';
        echo '<option value="UTC"' . selected( $value, 'UTC', false ) . '>UTC</option>';
        echo '<option value="America/New_York"' . selected( $value, 'America/New_York', false ) . '>Eastern Time</option>';
        echo '<option value="America/Chicago"' . selected( $value, 'America/Chicago', false ) . '>Central Time</option>';
        echo '<option value="America/Denver"' . selected( $value, 'America/Denver', false ) . '>Mountain Time</option>';
        echo '<option value="America/Los_Angeles"' . selected( $value, 'America/Los_Angeles', false ) . '>Pacific Time</option>';
        echo '</select>';
    }

    public function date_format_field_callback() {
        $value = get_option( 'schedspot_date_format', 'Y-m-d' );
        echo '<input type="text" name="schedspot_date_format" value="' . esc_attr( $value ) . '" />';
        echo '<p class="description">' . __( 'PHP date format. Default: Y-m-d', 'schedspot' ) . '</p>';
    }

    public function time_format_field_callback() {
        $value = get_option( 'schedspot_time_format', 'H:i' );
        echo '<input type="text" name="schedspot_time_format" value="' . esc_attr( $value ) . '" />';
        echo '<p class="description">' . __( 'PHP time format. Default: H:i', 'schedspot' ) . '</p>';
    }

    public function currency_field_callback() {
        $value = get_option( 'schedspot_currency', 'USD' );
        echo '<select name="schedspot_currency">';
        echo '<option value="USD"' . selected( $value, 'USD', false ) . '>USD ($)</option>';
        echo '<option value="EUR"' . selected( $value, 'EUR', false ) . '>EUR (€)</option>';
        echo '<option value="GBP"' . selected( $value, 'GBP', false ) . '>GBP (£)</option>';
        echo '</select>';
    }

    public function slot_length_field_callback() {
        $value = get_option( 'schedspot_default_slot_length', 60 );
        echo '<input type="number" name="schedspot_default_slot_length" value="' . esc_attr( $value ) . '" min="15" max="480" />';
        echo '<p class="description">' . __( 'Default booking slot length in minutes.', 'schedspot' ) . '</p>';
    }

    public function minimum_notice_field_callback() {
        $value = get_option( 'schedspot_minimum_notice', 24 );
        echo '<input type="number" name="schedspot_minimum_notice" value="' . esc_attr( $value ) . '" min="1" max="168" />';
        echo '<p class="description">' . __( 'Minimum notice required for bookings in hours.', 'schedspot' ) . '</p>';
    }

    public function auto_approve_field_callback() {
        $value = get_option( 'schedspot_auto_approve_bookings', 'no' );
        echo '<input type="checkbox" name="schedspot_auto_approve_bookings" value="yes"' . checked( $value, 'yes', false ) . ' />';
        echo '<label>' . __( 'Automatically approve new bookings', 'schedspot' ) . '</label>';
    }

    public function system_fee_field_callback() {
        $value = get_option( 'schedspot_system_fee_per_hour', 0 );
        echo '<input type="number" name="schedspot_system_fee_per_hour" value="' . esc_attr( $value ) . '" min="0" step="0.01" />';
        echo '<p class="description">' . __( 'Fixed fee added per hour of service.', 'schedspot' ) . '</p>';
    }

    public function commission_rate_field_callback() {
        $value = get_option( 'schedspot_commission_rate', 10 );
        echo '<input type="number" name="schedspot_commission_rate" value="' . esc_attr( $value ) . '" min="0" max="100" step="0.1" />';
        echo '<p class="description">' . __( 'Commission percentage taken from worker earnings.', 'schedspot' ) . '</p>';
    }

    /**
     * Render services list.
     *
     * @since 1.0.0
     */
    private function render_services_list() {
        $services = SchedSpot_Service::get_services( array( 'limit' => 50 ) );

        ?>
        <div class="wrap">
            <h1 class="wp-heading-inline"><?php _e( 'Services', 'schedspot' ); ?></h1>
            <a href="<?php echo admin_url( 'admin.php?page=schedspot-services&action=add' ); ?>" class="page-title-action"><?php _e( 'Add New', 'schedspot' ); ?></a>
            <hr class="wp-header-end">

            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e( 'Name', 'schedspot' ); ?></th>
                        <th><?php _e( 'Category', 'schedspot' ); ?></th>
                        <th><?php _e( 'Duration', 'schedspot' ); ?></th>
                        <th><?php _e( 'Price Type', 'schedspot' ); ?></th>
                        <th><?php _e( 'Base Price', 'schedspot' ); ?></th>
                        <th><?php _e( 'Status', 'schedspot' ); ?></th>
                        <th><?php _e( 'Actions', 'schedspot' ); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ( ! empty( $services ) ) : ?>
                        <?php foreach ( $services as $service ) : ?>
                            <tr>
                                <td><strong><?php echo esc_html( $service->name ); ?></strong></td>
                                <td><?php echo esc_html( $service->category ); ?></td>
                                <td><?php printf( __( '%d minutes', 'schedspot' ), $service->duration ); ?></td>
                                <td><?php echo esc_html( ucfirst( $service->price_type ) ); ?></td>
                                <td>$<?php echo esc_html( number_format( $service->base_price, 2 ) ); ?></td>
                                <td><?php echo $service->is_active ? __( 'Active', 'schedspot' ) : __( 'Inactive', 'schedspot' ); ?></td>
                                <td>
                                    <a href="<?php echo admin_url( 'admin.php?page=schedspot-services&action=edit&service_id=' . $service->id ); ?>" class="button button-small"><?php _e( 'Edit', 'schedspot' ); ?></a>
                                    <a href="<?php echo admin_url( 'admin.php?page=schedspot-services&action=delete&service_id=' . $service->id ); ?>" class="button button-small" onclick="return confirm('<?php _e( 'Are you sure you want to delete this service?', 'schedspot' ); ?>')"><?php _e( 'Delete', 'schedspot' ); ?></a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else : ?>
                        <tr>
                            <td colspan="7"><?php _e( 'No services found.', 'schedspot' ); ?></td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        <?php
    }

    /**
     * Render add service form.
     *
     * @since 1.0.0
     */
    private function render_add_service_form() {
        ?>
        <div class="wrap">
            <h1><?php _e( 'Add New Service', 'schedspot' ); ?></h1>

            <form method="post" action="">
                <?php wp_nonce_field( 'schedspot_service_form', 'schedspot_service_nonce' ); ?>
                <input type="hidden" name="schedspot_service_action" value="add">

                <table class="form-table">
                    <tr>
                        <th scope="row"><label for="service_name"><?php _e( 'Service Name', 'schedspot' ); ?> <span class="description">(required)</span></label></th>
                        <td><input type="text" id="service_name" name="service_name" class="regular-text" required></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="service_description"><?php _e( 'Description', 'schedspot' ); ?></label></th>
                        <td><textarea id="service_description" name="service_description" rows="4" cols="50"></textarea></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="service_category"><?php _e( 'Category', 'schedspot' ); ?></label></th>
                        <td>
                            <input type="text" id="service_category" name="service_category" class="regular-text" list="service_categories">
                            <datalist id="service_categories">
                                <?php foreach ( SchedSpot_Service::get_categories() as $category ) : ?>
                                    <option value="<?php echo esc_attr( $category ); ?>">
                                <?php endforeach; ?>
                            </datalist>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="service_duration"><?php _e( 'Duration (minutes)', 'schedspot' ); ?> <span class="description">(required)</span></label></th>
                        <td><input type="number" id="service_duration" name="service_duration" min="15" max="480" value="60" required></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="service_price_type"><?php _e( 'Price Type', 'schedspot' ); ?> <span class="description">(required)</span></label></th>
                        <td>
                            <select id="service_price_type" name="service_price_type" required>
                                <option value="hourly"><?php _e( 'Hourly', 'schedspot' ); ?></option>
                                <option value="fixed"><?php _e( 'Fixed Price', 'schedspot' ); ?></option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="service_base_price"><?php _e( 'Base Price ($)', 'schedspot' ); ?> <span class="description">(required)</span></label></th>
                        <td><input type="number" id="service_base_price" name="service_base_price" min="0" step="0.01" required></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="service_is_active"><?php _e( 'Status', 'schedspot' ); ?></label></th>
                        <td>
                            <label>
                                <input type="checkbox" id="service_is_active" name="service_is_active" value="1" checked>
                                <?php _e( 'Active', 'schedspot' ); ?>
                            </label>
                        </td>
                    </tr>
                </table>

                <?php submit_button( __( 'Add Service', 'schedspot' ) ); ?>
            </form>
        </div>
        <?php
    }

    /**
     * Render edit service form.
     *
     * @since 1.0.0
     * @param int $service_id Service ID.
     */
    private function render_edit_service_form( $service_id ) {
        $service = new SchedSpot_Service( $service_id );

        if ( ! $service->id ) {
            wp_die( __( 'Service not found.', 'schedspot' ) );
        }

        ?>
        <div class="wrap">
            <h1><?php _e( 'Edit Service', 'schedspot' ); ?></h1>

            <form method="post" action="">
                <?php wp_nonce_field( 'schedspot_service_form', 'schedspot_service_nonce' ); ?>
                <input type="hidden" name="schedspot_service_action" value="edit">
                <input type="hidden" name="service_id" value="<?php echo esc_attr( $service->id ); ?>">

                <table class="form-table">
                    <tr>
                        <th scope="row"><label for="service_name"><?php _e( 'Service Name', 'schedspot' ); ?> <span class="description">(required)</span></label></th>
                        <td><input type="text" id="service_name" name="service_name" class="regular-text" value="<?php echo esc_attr( $service->name ); ?>" required></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="service_description"><?php _e( 'Description', 'schedspot' ); ?></label></th>
                        <td><textarea id="service_description" name="service_description" rows="4" cols="50"><?php echo esc_textarea( $service->description ); ?></textarea></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="service_category"><?php _e( 'Category', 'schedspot' ); ?></label></th>
                        <td>
                            <input type="text" id="service_category" name="service_category" class="regular-text" value="<?php echo esc_attr( $service->category ); ?>" list="service_categories">
                            <datalist id="service_categories">
                                <?php foreach ( SchedSpot_Service::get_categories() as $category ) : ?>
                                    <option value="<?php echo esc_attr( $category ); ?>">
                                <?php endforeach; ?>
                            </datalist>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="service_duration"><?php _e( 'Duration (minutes)', 'schedspot' ); ?> <span class="description">(required)</span></label></th>
                        <td><input type="number" id="service_duration" name="service_duration" min="15" max="480" value="<?php echo esc_attr( $service->duration ); ?>" required></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="service_price_type"><?php _e( 'Price Type', 'schedspot' ); ?> <span class="description">(required)</span></label></th>
                        <td>
                            <select id="service_price_type" name="service_price_type" required>
                                <option value="hourly" <?php selected( $service->price_type, 'hourly' ); ?>><?php _e( 'Hourly', 'schedspot' ); ?></option>
                                <option value="fixed" <?php selected( $service->price_type, 'fixed' ); ?>><?php _e( 'Fixed Price', 'schedspot' ); ?></option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="service_base_price"><?php _e( 'Base Price ($)', 'schedspot' ); ?> <span class="description">(required)</span></label></th>
                        <td><input type="number" id="service_base_price" name="service_base_price" min="0" step="0.01" value="<?php echo esc_attr( $service->base_price ); ?>" required></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="service_is_active"><?php _e( 'Status', 'schedspot' ); ?></label></th>
                        <td>
                            <label>
                                <input type="checkbox" id="service_is_active" name="service_is_active" value="1" <?php checked( $service->is_active ); ?>>
                                <?php _e( 'Active', 'schedspot' ); ?>
                            </label>
                        </td>
                    </tr>
                </table>

                <?php submit_button( __( 'Update Service', 'schedspot' ) ); ?>
            </form>

            <h2><?php _e( 'Assigned Workers', 'schedspot' ); ?></h2>
            <?php $this->render_service_workers( $service ); ?>
        </div>
        <?php
    }

    /**
     * Render service workers section.
     *
     * @since 1.0.0
     * @param SchedSpot_Service $service Service object.
     */
    private function render_service_workers( $service ) {
        $workers = $service->get_workers();
        $all_workers = get_users( array( 'role' => 'schedspot_worker' ) );

        ?>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th><?php _e( 'Worker', 'schedspot' ); ?></th>
                    <th><?php _e( 'Custom Price', 'schedspot' ); ?></th>
                    <th><?php _e( 'Status', 'schedspot' ); ?></th>
                    <th><?php _e( 'Actions', 'schedspot' ); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php if ( ! empty( $workers ) ) : ?>
                    <?php foreach ( $workers as $worker ) : ?>
                        <tr>
                            <td><?php echo esc_html( $worker['name'] ); ?></td>
                            <td>$<?php echo esc_html( number_format( $worker['custom_price'], 2 ) ); ?></td>
                            <td><?php echo $worker['is_enabled'] ? __( 'Enabled', 'schedspot' ) : __( 'Disabled', 'schedspot' ); ?></td>
                            <td>
                                <a href="#" class="button button-small" onclick="removeWorkerFromService(<?php echo $worker['id']; ?>, <?php echo $service->id; ?>)"><?php _e( 'Remove', 'schedspot' ); ?></a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else : ?>
                    <tr>
                        <td colspan="4"><?php _e( 'No workers assigned to this service.', 'schedspot' ); ?></td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>

        <h3><?php _e( 'Assign Worker', 'schedspot' ); ?></h3>
        <form method="post" action="" style="margin-top: 20px;">
            <?php wp_nonce_field( 'schedspot_assign_worker', 'schedspot_assign_worker_nonce' ); ?>
            <input type="hidden" name="schedspot_service_action" value="assign_worker">
            <input type="hidden" name="service_id" value="<?php echo esc_attr( $service->id ); ?>">

            <table class="form-table">
                <tr>
                    <th scope="row"><label for="worker_id"><?php _e( 'Worker', 'schedspot' ); ?></label></th>
                    <td>
                        <select id="worker_id" name="worker_id" required>
                            <option value=""><?php _e( 'Select a worker', 'schedspot' ); ?></option>
                            <?php foreach ( $all_workers as $worker ) : ?>
                                <option value="<?php echo esc_attr( $worker->ID ); ?>"><?php echo esc_html( $worker->display_name ); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><label for="custom_price"><?php _e( 'Custom Price ($)', 'schedspot' ); ?></label></th>
                    <td>
                        <input type="number" id="custom_price" name="custom_price" min="0" step="0.01" value="<?php echo esc_attr( $service->base_price ); ?>">
                        <p class="description"><?php _e( 'Leave empty to use base price.', 'schedspot' ); ?></p>
                    </td>
                </tr>
            </table>

            <?php submit_button( __( 'Assign Worker', 'schedspot' ), 'secondary' ); ?>
        </form>
        <?php
    }

    /**
     * Handle service form submission.
     *
     * @since 1.0.0
     */
    private function handle_service_form_submission() {
        if ( ! wp_verify_nonce( $_POST['schedspot_service_nonce'], 'schedspot_service_form' ) ) {
            wp_die( __( 'Security check failed.', 'schedspot' ) );
        }

        $action = sanitize_text_field( $_POST['schedspot_service_action'] );

        $service_data = array(
            'name'        => sanitize_text_field( $_POST['service_name'] ),
            'description' => sanitize_textarea_field( $_POST['service_description'] ),
            'category'    => sanitize_text_field( $_POST['service_category'] ),
            'duration'    => absint( $_POST['service_duration'] ),
            'price_type'  => sanitize_text_field( $_POST['service_price_type'] ),
            'base_price'  => floatval( $_POST['service_base_price'] ),
            'is_active'   => isset( $_POST['service_is_active'] ) ? 1 : 0,
        );

        if ( $action === 'add' ) {
            $result = SchedSpot_Service::create_service( $service_data );

            if ( is_wp_error( $result ) ) {
                add_action( 'admin_notices', function() use ( $result ) {
                    echo '<div class="notice notice-error"><p>' . esc_html( $result->get_error_message() ) . '</p></div>';
                } );
            } else {
                add_action( 'admin_notices', function() {
                    echo '<div class="notice notice-success"><p>' . __( 'Service created successfully.', 'schedspot' ) . '</p></div>';
                } );
                wp_redirect( admin_url( 'admin.php?page=schedspot-services' ) );
                exit;
            }
        } elseif ( $action === 'edit' ) {
            $service_id = absint( $_POST['service_id'] );
            $service = new SchedSpot_Service( $service_id );

            if ( $service->id ) {
                $result = $service->update( $service_data );

                if ( $result ) {
                    add_action( 'admin_notices', function() {
                        echo '<div class="notice notice-success"><p>' . __( 'Service updated successfully.', 'schedspot' ) . '</p></div>';
                    } );
                } else {
                    add_action( 'admin_notices', function() {
                        echo '<div class="notice notice-error"><p>' . __( 'Failed to update service.', 'schedspot' ) . '</p></div>';
                    } );
                }
            }
        } elseif ( $action === 'assign_worker' ) {
            if ( ! wp_verify_nonce( $_POST['schedspot_assign_worker_nonce'], 'schedspot_assign_worker' ) ) {
                wp_die( __( 'Security check failed.', 'schedspot' ) );
            }

            $service_id = absint( $_POST['service_id'] );
            $worker_id = absint( $_POST['worker_id'] );
            $custom_price = ! empty( $_POST['custom_price'] ) ? floatval( $_POST['custom_price'] ) : null;

            $service = new SchedSpot_Service( $service_id );
            if ( $service->id ) {
                $result = $service->assign_worker( $worker_id, $custom_price );

                if ( $result ) {
                    add_action( 'admin_notices', function() {
                        echo '<div class="notice notice-success"><p>' . __( 'Worker assigned successfully.', 'schedspot' ) . '</p></div>';
                    } );
                } else {
                    add_action( 'admin_notices', function() {
                        echo '<div class="notice notice-error"><p>' . __( 'Failed to assign worker.', 'schedspot' ) . '</p></div>';
                    } );
                }
            }
        }
    }

    /**
     * Handle delete service.
     *
     * @since 1.0.0
     * @param int $service_id Service ID.
     */
    private function handle_delete_service( $service_id ) {
        if ( ! wp_verify_nonce( $_GET['_wpnonce'], 'delete_service_' . $service_id ) ) {
            wp_die( __( 'Security check failed.', 'schedspot' ) );
        }

        $service = new SchedSpot_Service( $service_id );

        if ( $service->id ) {
            $result = $service->delete();

            if ( is_wp_error( $result ) ) {
                add_action( 'admin_notices', function() use ( $result ) {
                    echo '<div class="notice notice-error"><p>' . esc_html( $result->get_error_message() ) . '</p></div>';
                } );
            } else {
                add_action( 'admin_notices', function() {
                    echo '<div class="notice notice-success"><p>' . __( 'Service deleted successfully.', 'schedspot' ) . '</p></div>';
                } );
            }
        }

        wp_redirect( admin_url( 'admin.php?page=schedspot-services' ) );
        exit;
    }

    /**
     * Render add worker form.
     *
     * @since 1.0.0
     */
    private function render_add_worker_form() {
        $users = get_users( array(
            'meta_query' => array(
                array(
                    'key'     => 'wp_capabilities',
                    'value'   => 'schedspot_worker',
                    'compare' => 'NOT LIKE'
                )
            )
        ) );

        ?>
        <div class="wrap">
            <h1><?php _e( 'Add New Worker', 'schedspot' ); ?></h1>

            <form method="post" action="">
                <?php wp_nonce_field( 'schedspot_worker_form', 'schedspot_worker_nonce' ); ?>
                <input type="hidden" name="schedspot_worker_action" value="add">

                <table class="form-table">
                    <tr>
                        <th scope="row"><label for="user_id"><?php _e( 'Select User', 'schedspot' ); ?> <span class="description">(required)</span></label></th>
                        <td>
                            <select id="user_id" name="user_id" required>
                                <option value=""><?php _e( 'Select a user', 'schedspot' ); ?></option>
                                <?php foreach ( $users as $user ) : ?>
                                    <option value="<?php echo esc_attr( $user->ID ); ?>">
                                        <?php echo esc_html( $user->display_name . ' (' . $user->user_email . ')' ); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <p class="description"><?php _e( 'Select an existing user to convert to a worker, or create a new user first.', 'schedspot' ); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="bio"><?php _e( 'Bio', 'schedspot' ); ?></label></th>
                        <td><textarea id="bio" name="bio" rows="4" cols="50" placeholder="<?php _e( 'Tell us about your experience and skills...', 'schedspot' ); ?>"></textarea></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="hourly_rate"><?php _e( 'Hourly Rate ($)', 'schedspot' ); ?></label></th>
                        <td><input type="number" id="hourly_rate" name="hourly_rate" min="0" step="0.01" placeholder="25.00"></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="phone"><?php _e( 'Phone Number', 'schedspot' ); ?></label></th>
                        <td><input type="tel" id="phone" name="phone" class="regular-text"></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="experience_years"><?php _e( 'Years of Experience', 'schedspot' ); ?></label></th>
                        <td><input type="number" id="experience_years" name="experience_years" min="0" max="50"></td>
                    </tr>
                </table>

                <?php submit_button( __( 'Add Worker', 'schedspot' ) ); ?>
            </form>
        </div>
        <?php
    }

    /**
     * Render edit worker form.
     *
     * @since 1.0.0
     * @param int $worker_id Worker ID.
     */
    private function render_edit_worker_form( $worker_id ) {
        $worker = new SchedSpot_Worker( $worker_id );

        if ( ! $worker->id ) {
            wp_die( __( 'Worker not found.', 'schedspot' ) );
        }

        ?>
        <div class="wrap">
            <h1><?php printf( __( 'Edit Worker: %s', 'schedspot' ), esc_html( $worker->user->display_name ) ); ?></h1>

            <form method="post" action="">
                <?php wp_nonce_field( 'schedspot_worker_form', 'schedspot_worker_nonce' ); ?>
                <input type="hidden" name="schedspot_worker_action" value="edit">
                <input type="hidden" name="worker_id" value="<?php echo esc_attr( $worker->id ); ?>">

                <table class="form-table">
                    <tr>
                        <th scope="row"><label for="bio"><?php _e( 'Bio', 'schedspot' ); ?></label></th>
                        <td><textarea id="bio" name="bio" rows="4" cols="50"><?php echo esc_textarea( $worker->profile['bio'] ); ?></textarea></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="skills"><?php _e( 'Skills', 'schedspot' ); ?></label></th>
                        <td>
                            <input type="text" id="skills" name="skills" class="regular-text" value="<?php echo esc_attr( implode( ', ', $worker->profile['skills'] ) ); ?>">
                            <p class="description"><?php _e( 'Separate skills with commas.', 'schedspot' ); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="hourly_rate"><?php _e( 'Hourly Rate ($)', 'schedspot' ); ?></label></th>
                        <td><input type="number" id="hourly_rate" name="hourly_rate" min="0" step="0.01" value="<?php echo esc_attr( $worker->profile['hourly_rate'] ); ?>"></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="phone"><?php _e( 'Phone Number', 'schedspot' ); ?></label></th>
                        <td><input type="tel" id="phone" name="phone" class="regular-text" value="<?php echo esc_attr( $worker->profile['phone'] ); ?>"></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="address"><?php _e( 'Address', 'schedspot' ); ?></label></th>
                        <td><textarea id="address" name="address" rows="3" cols="50"><?php echo esc_textarea( $worker->profile['address'] ); ?></textarea></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="experience_years"><?php _e( 'Years of Experience', 'schedspot' ); ?></label></th>
                        <td><input type="number" id="experience_years" name="experience_years" min="0" max="50" value="<?php echo esc_attr( $worker->profile['experience_years'] ); ?>"></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="certifications"><?php _e( 'Certifications', 'schedspot' ); ?></label></th>
                        <td>
                            <input type="text" id="certifications" name="certifications" class="regular-text" value="<?php echo esc_attr( implode( ', ', $worker->profile['certifications'] ) ); ?>">
                            <p class="description"><?php _e( 'Separate certifications with commas.', 'schedspot' ); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="languages"><?php _e( 'Languages', 'schedspot' ); ?></label></th>
                        <td>
                            <input type="text" id="languages" name="languages" class="regular-text" value="<?php echo esc_attr( implode( ', ', $worker->profile['languages'] ) ); ?>">
                            <p class="description"><?php _e( 'Separate languages with commas.', 'schedspot' ); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="service_areas"><?php _e( 'Service Areas', 'schedspot' ); ?></label></th>
                        <td>
                            <input type="text" id="service_areas" name="service_areas" class="regular-text" value="<?php echo esc_attr( implode( ', ', $worker->profile['service_areas'] ) ); ?>">
                            <p class="description"><?php _e( 'Areas where this worker provides services. Separate with commas.', 'schedspot' ); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="availability_note"><?php _e( 'Availability Note', 'schedspot' ); ?></label></th>
                        <td><textarea id="availability_note" name="availability_note" rows="3" cols="50"><?php echo esc_textarea( $worker->profile['availability_note'] ); ?></textarea></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="is_available"><?php _e( 'Status', 'schedspot' ); ?></label></th>
                        <td>
                            <label>
                                <input type="checkbox" id="is_available" name="is_available" value="1" <?php checked( $worker->profile['is_available'] ); ?>>
                                <?php _e( 'Available for new bookings', 'schedspot' ); ?>
                            </label>
                        </td>
                    </tr>
                </table>

                <?php submit_button( __( 'Update Worker', 'schedspot' ) ); ?>
            </form>

            <h2><?php _e( 'Worker Statistics', 'schedspot' ); ?></h2>
            <?php $this->render_worker_statistics( $worker ); ?>
        </div>
        <?php
    }

    /**
     * Render worker profile view.
     *
     * @since 1.0.0
     * @param int $worker_id Worker ID.
     */
    private function render_worker_profile( $worker_id ) {
        $worker = new SchedSpot_Worker( $worker_id );

        if ( ! $worker->id ) {
            wp_die( __( 'Worker not found.', 'schedspot' ) );
        }

        $stats = $worker->get_statistics();
        $services = $worker->get_services();

        ?>
        <div class="wrap">
            <h1><?php printf( __( 'Worker Profile: %s', 'schedspot' ), esc_html( $worker->user->display_name ) ); ?></h1>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 20px;">
                <div class="schedspot-widget">
                    <h3><?php _e( 'Profile Information', 'schedspot' ); ?></h3>
                    <table class="form-table">
                        <tr>
                            <th><?php _e( 'Name', 'schedspot' ); ?></th>
                            <td><?php echo esc_html( $worker->user->display_name ); ?></td>
                        </tr>
                        <tr>
                            <th><?php _e( 'Email', 'schedspot' ); ?></th>
                            <td><?php echo esc_html( $worker->user->user_email ); ?></td>
                        </tr>
                        <tr>
                            <th><?php _e( 'Phone', 'schedspot' ); ?></th>
                            <td><?php echo esc_html( $worker->profile['phone'] ); ?></td>
                        </tr>
                        <tr>
                            <th><?php _e( 'Hourly Rate', 'schedspot' ); ?></th>
                            <td>$<?php echo esc_html( number_format( $worker->profile['hourly_rate'], 2 ) ); ?></td>
                        </tr>
                        <tr>
                            <th><?php _e( 'Experience', 'schedspot' ); ?></th>
                            <td><?php printf( __( '%d years', 'schedspot' ), $worker->profile['experience_years'] ); ?></td>
                        </tr>
                        <tr>
                            <th><?php _e( 'Profile Completion', 'schedspot' ); ?></th>
                            <td><?php echo esc_html( $stats['profile_completion'] ); ?>%</td>
                        </tr>
                    </table>
                </div>

                <div class="schedspot-widget">
                    <h3><?php _e( 'Statistics', 'schedspot' ); ?></h3>
                    <div class="schedspot-stats-grid">
                        <div class="stat-item">
                            <div class="stat-number"><?php echo esc_html( $stats['total_bookings'] ); ?></div>
                            <div class="stat-label"><?php _e( 'Total Bookings', 'schedspot' ); ?></div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">$<?php echo esc_html( number_format( $stats['total_earnings'], 2 ) ); ?></div>
                            <div class="stat-label"><?php _e( 'Total Earnings', 'schedspot' ); ?></div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number"><?php echo esc_html( $stats['completion_rate'] ); ?>%</div>
                            <div class="stat-label"><?php _e( 'Completion Rate', 'schedspot' ); ?></div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number"><?php echo esc_html( number_format( $stats['rating'], 1 ) ); ?></div>
                            <div class="stat-label"><?php _e( 'Rating', 'schedspot' ); ?></div>
                        </div>
                    </div>
                </div>
            </div>

            <?php if ( ! empty( $worker->profile['bio'] ) ) : ?>
                <div class="schedspot-widget" style="margin-top: 20px;">
                    <h3><?php _e( 'Bio', 'schedspot' ); ?></h3>
                    <p><?php echo esc_html( $worker->profile['bio'] ); ?></p>
                </div>
            <?php endif; ?>

            <div class="schedspot-widget" style="margin-top: 20px;">
                <h3><?php _e( 'Services', 'schedspot' ); ?></h3>
                <?php if ( ! empty( $services ) ) : ?>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th><?php _e( 'Service', 'schedspot' ); ?></th>
                                <th><?php _e( 'Category', 'schedspot' ); ?></th>
                                <th><?php _e( 'Duration', 'schedspot' ); ?></th>
                                <th><?php _e( 'Price', 'schedspot' ); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ( $services as $service ) : ?>
                                <tr>
                                    <td><?php echo esc_html( $service['name'] ); ?></td>
                                    <td><?php echo esc_html( $service['category'] ); ?></td>
                                    <td><?php printf( __( '%d minutes', 'schedspot' ), $service['duration'] ); ?></td>
                                    <td>$<?php echo esc_html( number_format( $service['custom_price'], 2 ) ); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php else : ?>
                    <p><?php _e( 'No services assigned to this worker.', 'schedspot' ); ?></p>
                <?php endif; ?>
            </div>

            <div style="margin-top: 20px;">
                <a href="<?php echo admin_url( 'admin.php?page=schedspot-workers&action=edit&worker_id=' . $worker->id ); ?>" class="button button-primary"><?php _e( 'Edit Profile', 'schedspot' ); ?></a>
                <a href="<?php echo admin_url( 'admin.php?page=schedspot-workers&action=availability&worker_id=' . $worker->id ); ?>" class="button"><?php _e( 'Manage Availability', 'schedspot' ); ?></a>
                <a href="<?php echo admin_url( 'admin.php?page=schedspot-workers' ); ?>" class="button"><?php _e( 'Back to Workers', 'schedspot' ); ?></a>
            </div>
        </div>
        <?php
    }

    /**
     * Render worker availability management.
     *
     * @since 1.0.0
     * @param int $worker_id Worker ID.
     */
    private function render_worker_availability( $worker_id ) {
        $worker = new SchedSpot_Worker( $worker_id );

        if ( ! $worker->id ) {
            wp_die( __( 'Worker not found.', 'schedspot' ) );
        }

        $availability = $worker->get_availability();
        $days = array(
            1 => __( 'Monday', 'schedspot' ),
            2 => __( 'Tuesday', 'schedspot' ),
            3 => __( 'Wednesday', 'schedspot' ),
            4 => __( 'Thursday', 'schedspot' ),
            5 => __( 'Friday', 'schedspot' ),
            6 => __( 'Saturday', 'schedspot' ),
            0 => __( 'Sunday', 'schedspot' ),
        );

        ?>
        <div class="wrap">
            <h1><?php printf( __( 'Availability: %s', 'schedspot' ), esc_html( $worker->user->display_name ) ); ?></h1>

            <form method="post" action="">
                <?php wp_nonce_field( 'schedspot_availability_form', 'schedspot_availability_nonce' ); ?>
                <input type="hidden" name="schedspot_worker_action" value="update_availability">
                <input type="hidden" name="worker_id" value="<?php echo esc_attr( $worker->id ); ?>">

                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e( 'Day', 'schedspot' ); ?></th>
                            <th><?php _e( 'Available', 'schedspot' ); ?></th>
                            <th><?php _e( 'Start Time', 'schedspot' ); ?></th>
                            <th><?php _e( 'End Time', 'schedspot' ); ?></th>
                            <th><?php _e( 'Actions', 'schedspot' ); ?></th>
                        </tr>
                    </thead>
                    <tbody id="availability-schedule">
                        <?php foreach ( $days as $day_num => $day_name ) : ?>
                            <?php
                            $day_slots = array_filter( $availability, function( $slot ) use ( $day_num ) {
                                return $slot['day_of_week'] == $day_num;
                            } );

                            if ( empty( $day_slots ) ) {
                                $day_slots = array( array(
                                    'id' => 0,
                                    'day_of_week' => $day_num,
                                    'start_time' => '09:00:00',
                                    'end_time' => '17:00:00',
                                    'is_available' => false
                                ) );
                            }
                            ?>

                            <?php foreach ( $day_slots as $index => $slot ) : ?>
                                <tr data-day="<?php echo esc_attr( $day_num ); ?>">
                                    <td>
                                        <?php if ( $index === 0 ) echo esc_html( $day_name ); ?>
                                        <input type="hidden" name="availability[<?php echo esc_attr( $day_num ); ?>][<?php echo esc_attr( $index ); ?>][day_of_week]" value="<?php echo esc_attr( $day_num ); ?>">
                                    </td>
                                    <td>
                                        <input type="checkbox" name="availability[<?php echo esc_attr( $day_num ); ?>][<?php echo esc_attr( $index ); ?>][is_available]" value="1" <?php checked( $slot['is_available'] ); ?>>
                                    </td>
                                    <td>
                                        <input type="time" name="availability[<?php echo esc_attr( $day_num ); ?>][<?php echo esc_attr( $index ); ?>][start_time]" value="<?php echo esc_attr( substr( $slot['start_time'], 0, 5 ) ); ?>">
                                    </td>
                                    <td>
                                        <input type="time" name="availability[<?php echo esc_attr( $day_num ); ?>][<?php echo esc_attr( $index ); ?>][end_time]" value="<?php echo esc_attr( substr( $slot['end_time'], 0, 5 ) ); ?>">
                                    </td>
                                    <td>
                                        <?php if ( count( $day_slots ) > 1 ) : ?>
                                            <button type="button" class="button button-small remove-slot"><?php _e( 'Remove', 'schedspot' ); ?></button>
                                        <?php endif; ?>
                                        <?php if ( $index === count( $day_slots ) - 1 ) : ?>
                                            <button type="button" class="button button-small add-slot" data-day="<?php echo esc_attr( $day_num ); ?>"><?php _e( 'Add Slot', 'schedspot' ); ?></button>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endforeach; ?>
                    </tbody>
                </table>

                <p class="description"><?php _e( 'Set the worker\'s regular weekly availability. You can add multiple time slots per day.', 'schedspot' ); ?></p>

                <?php submit_button( __( 'Update Availability', 'schedspot' ) ); ?>
            </form>

            <div style="margin-top: 20px;">
                <a href="<?php echo admin_url( 'admin.php?page=schedspot-workers&action=view&worker_id=' . $worker->id ); ?>" class="button"><?php _e( 'View Profile', 'schedspot' ); ?></a>
                <a href="<?php echo admin_url( 'admin.php?page=schedspot-workers' ); ?>" class="button"><?php _e( 'Back to Workers', 'schedspot' ); ?></a>
            </div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            // Add new time slot
            $('.add-slot').on('click', function() {
                var day = $(this).data('day');
                var row = $(this).closest('tr');
                var index = $('tr[data-day="' + day + '"]').length;

                var newRow = row.clone();
                newRow.find('td:first').html('<input type="hidden" name="availability[' + day + '][' + index + '][day_of_week]" value="' + day + '">');
                newRow.find('input[type="checkbox"]').attr('name', 'availability[' + day + '][' + index + '][is_available]').prop('checked', false);
                newRow.find('input[type="time"]:first').attr('name', 'availability[' + day + '][' + index + '][start_time]').val('09:00');
                newRow.find('input[type="time"]:last').attr('name', 'availability[' + day + '][' + index + '][end_time]').val('17:00');
                newRow.find('.add-slot').remove();

                row.after(newRow);
                row.find('.add-slot').remove();
                newRow.find('td:last').append('<button type="button" class="button button-small add-slot" data-day="' + day + '"><?php _e( 'Add Slot', 'schedspot' ); ?></button>');
            });

            // Remove time slot
            $(document).on('click', '.remove-slot', function() {
                $(this).closest('tr').remove();
            });
        });
        </script>
        <?php
    }

    /**
     * Render worker statistics.
     *
     * @since 1.0.0
     * @param SchedSpot_Worker $worker Worker object.
     */
    private function render_worker_statistics( $worker ) {
        $stats = $worker->get_statistics();

        ?>
        <div class="schedspot-stats-grid" style="grid-template-columns: repeat(4, 1fr);">
            <div class="stat-item">
                <div class="stat-number"><?php echo esc_html( $stats['total_bookings'] ); ?></div>
                <div class="stat-label"><?php _e( 'Total Bookings', 'schedspot' ); ?></div>
            </div>
            <div class="stat-item">
                <div class="stat-number">$<?php echo esc_html( number_format( $stats['total_earnings'], 2 ) ); ?></div>
                <div class="stat-label"><?php _e( 'Total Earnings', 'schedspot' ); ?></div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?php echo esc_html( $stats['month_bookings'] ); ?></div>
                <div class="stat-label"><?php _e( 'This Month', 'schedspot' ); ?></div>
            </div>
            <div class="stat-item">
                <div class="stat-number">$<?php echo esc_html( number_format( $stats['month_earnings'], 2 ) ); ?></div>
                <div class="stat-label"><?php _e( 'Month Earnings', 'schedspot' ); ?></div>
            </div>
        </div>
        <?php
    }

    /**
     * Handle worker form submission.
     *
     * @since 1.0.0
     */
    private function handle_worker_form_submission() {
        if ( ! wp_verify_nonce( $_POST['schedspot_worker_nonce'], 'schedspot_worker_form' ) &&
             ! wp_verify_nonce( $_POST['schedspot_availability_nonce'], 'schedspot_availability_form' ) ) {
            wp_die( __( 'Security check failed.', 'schedspot' ) );
        }

        $action = sanitize_text_field( $_POST['schedspot_worker_action'] );

        if ( $action === 'add' ) {
            $user_id = absint( $_POST['user_id'] );
            $profile_data = array(
                'bio'              => sanitize_textarea_field( $_POST['bio'] ),
                'hourly_rate'      => floatval( $_POST['hourly_rate'] ),
                'phone'            => sanitize_text_field( $_POST['phone'] ),
                'experience_years' => absint( $_POST['experience_years'] ),
            );

            $result = SchedSpot_Worker::create_worker( $user_id, $profile_data );

            if ( is_wp_error( $result ) ) {
                add_action( 'admin_notices', function() use ( $result ) {
                    echo '<div class="notice notice-error"><p>' . esc_html( $result->get_error_message() ) . '</p></div>';
                } );
            } else {
                add_action( 'admin_notices', function() {
                    echo '<div class="notice notice-success"><p>' . __( 'Worker created successfully.', 'schedspot' ) . '</p></div>';
                } );
                wp_redirect( admin_url( 'admin.php?page=schedspot-workers' ) );
                exit;
            }
        } elseif ( $action === 'edit' ) {
            $worker_id = absint( $_POST['worker_id'] );
            $worker = new SchedSpot_Worker( $worker_id );

            if ( $worker->id ) {
                $profile_data = array(
                    'bio'               => sanitize_textarea_field( $_POST['bio'] ),
                    'skills'            => sanitize_text_field( $_POST['skills'] ),
                    'hourly_rate'       => floatval( $_POST['hourly_rate'] ),
                    'phone'             => sanitize_text_field( $_POST['phone'] ),
                    'address'           => sanitize_textarea_field( $_POST['address'] ),
                    'experience_years'  => absint( $_POST['experience_years'] ),
                    'certifications'    => sanitize_text_field( $_POST['certifications'] ),
                    'languages'         => sanitize_text_field( $_POST['languages'] ),
                    'service_areas'     => sanitize_text_field( $_POST['service_areas'] ),
                    'availability_note' => sanitize_textarea_field( $_POST['availability_note'] ),
                    'is_available'      => isset( $_POST['is_available'] ) ? 1 : 0,
                );

                $result = $worker->update_profile( $profile_data );

                if ( $result ) {
                    add_action( 'admin_notices', function() {
                        echo '<div class="notice notice-success"><p>' . __( 'Worker updated successfully.', 'schedspot' ) . '</p></div>';
                    } );
                } else {
                    add_action( 'admin_notices', function() {
                        echo '<div class="notice notice-error"><p>' . __( 'Failed to update worker.', 'schedspot' ) . '</p></div>';
                    } );
                }
            }
        } elseif ( $action === 'update_availability' ) {
            $worker_id = absint( $_POST['worker_id'] );
            $availability_data = $_POST['availability'];

            global $wpdb;

            // Clear existing availability
            $wpdb->delete(
                $wpdb->prefix . 'schedspot_worker_availability',
                array( 'worker_id' => $worker_id ),
                array( '%d' )
            );

            // Insert new availability
            foreach ( $availability_data as $day => $slots ) {
                foreach ( $slots as $slot ) {
                    if ( ! empty( $slot['is_available'] ) ) {
                        $wpdb->insert(
                            $wpdb->prefix . 'schedspot_worker_availability',
                            array(
                                'worker_id'    => $worker_id,
                                'day_of_week'  => absint( $slot['day_of_week'] ),
                                'start_time'   => sanitize_text_field( $slot['start_time'] ) . ':00',
                                'end_time'     => sanitize_text_field( $slot['end_time'] ) . ':00',
                                'is_available' => 1,
                            ),
                            array( '%d', '%d', '%s', '%s', '%d' )
                        );
                    }
                }
            }

            add_action( 'admin_notices', function() {
                echo '<div class="notice notice-success"><p>' . __( 'Availability updated successfully.', 'schedspot' ) . '</p></div>';
            } );
        }
    }

    /**
     * Get admin styles.
     *
     * @since 0.1.0
     * @return string CSS styles.
     */
    private function get_admin_styles() {
        return '
        .schedspot-dashboard-widgets {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .schedspot-widget {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            padding: 20px;
        }
        .schedspot-widget h3 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #23282d;
        }
        .schedspot-stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 4px;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #0073aa;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .schedspot-quick-actions {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .schedspot-placeholder {
            text-align: center;
            padding: 40px;
            background: #f9f9f9;
            border-radius: 4px;
            margin-top: 20px;
        }
        ';
    }
}
